import { databaseService, generateSyncId } from './database'
import { createMySQLClient, loadMySQLConfigFromStorage, validateMySQLConfig, defaultMySQLConfig } from './mysqlApi'
import type { JobApplication } from '../types/jobApplication'

export interface DualDatabaseResult {
  success: boolean
  localSuccess: boolean
  mysqlSuccess: boolean
  data?: any
  error?: string
  localError?: string
  mysqlError?: string
}

export interface DualDatabaseConfig {
  enableMySQL: boolean
  rollbackOnFailure: boolean
  retryAttempts: number
  retryDelay: number
}

interface FailedOperation {
  id: string
  type: 'add' | 'delete'
  data: any
  timestamp: number
  attempts: number
}

/**
 * 双数据库服务 - 同时操作本地数据库和MySQL数据库
 */
export class DualDatabaseService {
  private config: DualDatabaseConfig = {
    enableMySQL: true,
    rollbackOnFailure: true,
    retryAttempts: 3,
    retryDelay: 1000
  }

  private failedOperations: FailedOperation[] = []
  private retryTimer: NodeJS.Timeout | null = null

  /**
   * 设置双数据库配置
   */
  setConfig(config: Partial<DualDatabaseConfig>) {
    this.config = { ...this.config, ...config }
  }

  /**
   * 延迟函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * 带重试的操作执行器
   */
  private async executeWithRetry<T>(
    operation: () => Promise<T>,
    operationName: string,
    maxRetries: number = this.config.retryAttempts
  ): Promise<T> {
    let lastError: any

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation()
      } catch (error) {
        lastError = error
        console.warn(`${operationName} 失败 (尝试 ${attempt}/${maxRetries}):`, error)

        if (attempt < maxRetries) {
          await this.delay(this.config.retryDelay * attempt)
        }
      }
    }

    throw lastError
  }

  /**
   * 检查MySQL连接是否可用
   */
  private async isMySQLAvailable(): Promise<boolean> {
    if (!this.config.enableMySQL) {
      return false
    }

    try {
      const config = loadMySQLConfigFromStorage() || defaultMySQLConfig
      if (!validateMySQLConfig(config)) {
        console.warn('MySQL配置无效')
        return false
      }

      return await this.executeWithRetry(async () => {
        const client = createMySQLClient(config)
        try {
          const isConnected = await client.testConnection()
          return isConnected
        } finally {
          await client.close()
        }
      }, 'MySQL连接检查', 2) // 连接检查只重试2次
    } catch (error) {
      console.warn('MySQL连接检查失败:', error)
      return false
    }
  }

  /**
   * 添加求职申请到双数据库
   */
  async addJobApplication(application: Omit<JobApplication, 'id' | 'createdAt' | 'updatedAt'>): Promise<DualDatabaseResult> {
    const result: DualDatabaseResult = {
      success: false,
      localSuccess: false,
      mysqlSuccess: false
    }

    // 生成syncId（如果没有提供）
    const syncId = application.syncId || generateSyncId()
    const applicationWithSyncId = { ...application, syncId }

    let localId: number | null = null

    try {
      // 1. 首先添加到本地数据库
      console.log('开始添加到本地数据库...')
      localId = await databaseService.addJobApplication(applicationWithSyncId)
      result.localSuccess = true
      result.data = { localId, syncId }
      console.log('本地数据库添加成功，ID:', localId)

      // 2. 检查MySQL是否可用
      const mysqlAvailable = await this.isMySQLAvailable()
      if (!mysqlAvailable) {
        console.warn('MySQL不可用，仅保存到本地数据库')
        result.mysqlSuccess = false
        result.mysqlError = 'MySQL连接不可用'
        result.success = true // 本地成功即可
        return result
      }

      // 3. 添加到MySQL数据库
      console.log('开始添加到MySQL数据库...')
      const config = loadMySQLConfigFromStorage() || defaultMySQLConfig

      try {
        const mysqlId = await this.executeWithRetry(async () => {
          const client = createMySQLClient(config)
          try {
            const mysqlApp = client.convertToMySQLJobApplication(applicationWithSyncId)
            return await client.addJobApplication(mysqlApp)
          } finally {
            await client.close()
          }
        }, 'MySQL添加操作')

        result.mysqlSuccess = true
        result.data = { ...result.data, mysqlId }
        console.log('MySQL数据库添加成功，ID:', mysqlId)
      } catch (mysqlError) {
        result.mysqlSuccess = false
        result.mysqlError = mysqlError instanceof Error ? mysqlError.message : '未知错误'
        console.error('MySQL添加失败:', mysqlError)
      }

      result.success = result.localSuccess && result.mysqlSuccess
      return result

    } catch (error) {
      console.error('双数据库添加操作失败:', error)
      
      if (result.localSuccess && !result.mysqlSuccess) {
        result.mysqlError = error instanceof Error ? error.message : '未知错误'
        
        // 如果配置了失败回滚且本地成功但MySQL失败
        if (this.config.rollbackOnFailure && localId) {
          try {
            console.log('MySQL添加失败，回滚本地数据库操作...')
            await databaseService.deleteJobApplication(localId)
            result.localSuccess = false
            result.error = '操作失败，已回滚本地数据'
          } catch (rollbackError) {
            console.error('回滚本地数据失败:', rollbackError)
            result.error = '操作失败，回滚也失败，数据可能不一致'
          }
        } else {
          result.success = true // 本地成功即可
          result.error = 'MySQL添加失败，但本地数据已保存，将稍后重试'
          // 将失败的操作加入重试队列
          this.addFailedOperation('add', applicationWithSyncId)
        }
      } else {
        result.localError = error instanceof Error ? error.message : '未知错误'
        result.error = '本地数据库添加失败'
      }

      result.success = false
      return result
    }
  }

  /**
   * 从双数据库删除求职申请
   */
  async deleteJobApplication(id: number, syncId?: string): Promise<DualDatabaseResult> {
    const result: DualDatabaseResult = {
      success: false,
      localSuccess: false,
      mysqlSuccess: false
    }

    let actualSyncId = syncId

    try {
      // 1. 如果没有提供syncId，从本地数据库获取
      if (!actualSyncId) {
        // 获取所有应用，找到对应ID的syncId
        const allApps = await databaseService.getAllJobApplications()
        const targetApp = allApps.find(app => app.id === id)
        if (targetApp && targetApp.syncId) {
          actualSyncId = targetApp.syncId
        } else {
          console.warn('未找到对应的syncId，将仅删除本地记录')
        }
      }

      // 2. 删除本地数据库记录
      console.log('开始从本地数据库删除...')
      await databaseService.deleteJobApplication(id)
      result.localSuccess = true
      console.log('本地数据库删除成功')

      // 3. 检查MySQL是否可用
      const mysqlAvailable = await this.isMySQLAvailable()
      if (!mysqlAvailable) {
        console.warn('MySQL不可用，仅从本地数据库删除')
        result.mysqlSuccess = false
        result.mysqlError = 'MySQL连接不可用'
        result.success = true // 本地成功即可
        return result
      }

      // 4. 删除MySQL数据库记录
      if (actualSyncId) {
        console.log('开始从MySQL数据库删除...')
        const config = loadMySQLConfigFromStorage() || defaultMySQLConfig

        try {
          const deleted = await this.executeWithRetry(async () => {
            const client = createMySQLClient(config)
            try {
              return await client.deleteJobApplicationBySyncId(actualSyncId!)
            } finally {
              await client.close()
            }
          }, 'MySQL删除操作')

          result.mysqlSuccess = deleted
          if (deleted) {
            console.log('MySQL数据库删除成功')
          } else {
            console.warn('MySQL数据库中未找到对应记录')
            result.mysqlSuccess = true // 记录不存在也算成功
          }
        } catch (mysqlError) {
          result.mysqlSuccess = false
          result.mysqlError = mysqlError instanceof Error ? mysqlError.message : '未知错误'
          console.error('MySQL删除失败:', mysqlError)
        }
      }

      result.success = result.localSuccess && result.mysqlSuccess
      return result

    } catch (error) {
      console.error('双数据库删除操作失败:', error)
      
      if (!result.localSuccess) {
        result.localError = error instanceof Error ? error.message : '未知错误'
        result.error = '本地数据库删除失败'
      } else {
        result.mysqlError = error instanceof Error ? error.message : '未知错误'
        result.success = true // 本地成功即可
        result.error = 'MySQL删除失败，但本地数据已删除，将稍后重试'
        // 将失败的操作加入重试队列
        if (actualSyncId) {
          this.addFailedOperation('delete', { syncId: actualSyncId })
        }
      }

      return result
    }
  }

  /**
   * 添加失败操作到队列
   */
  private addFailedOperation(type: 'add' | 'delete', data: any): void {
    const operation: FailedOperation = {
      id: `${type}_${Date.now()}_${Math.random().toString(36).substring(2)}`,
      type,
      data,
      timestamp: Date.now(),
      attempts: 0
    }

    this.failedOperations.push(operation)
    this.scheduleRetry()
  }

  /**
   * 安排重试失败的操作
   */
  private scheduleRetry(): void {
    if (this.retryTimer) {
      return // 已经有重试计划了
    }

    this.retryTimer = setTimeout(async () => {
      await this.retryFailedOperations()
      this.retryTimer = null

      // 如果还有失败的操作，继续安排重试
      if (this.failedOperations.length > 0) {
        this.scheduleRetry()
      }
    }, 30000) // 30秒后重试
  }

  /**
   * 重试失败的操作
   */
  private async retryFailedOperations(): Promise<void> {
    if (this.failedOperations.length === 0) {
      return
    }

    console.log(`开始重试 ${this.failedOperations.length} 个失败的操作`)

    const operationsToRetry = [...this.failedOperations]
    this.failedOperations = []

    for (const operation of operationsToRetry) {
      operation.attempts++

      try {
        if (operation.type === 'add') {
          // 重试添加操作（仅MySQL部分）
          await this.retryMySQLAdd(operation.data)
        } else if (operation.type === 'delete') {
          // 重试删除操作（仅MySQL部分）
          await this.retryMySQLDelete(operation.data)
        }

        console.log(`重试操作成功: ${operation.id}`)
      } catch (error) {
        console.warn(`重试操作失败: ${operation.id}`, error)

        // 如果重试次数未超过限制，重新加入队列
        if (operation.attempts < this.config.retryAttempts) {
          this.failedOperations.push(operation)
        } else {
          console.error(`操作最终失败，放弃重试: ${operation.id}`)
        }
      }
    }
  }

  /**
   * 重试MySQL添加操作
   */
  private async retryMySQLAdd(data: any): Promise<void> {
    const config = loadMySQLConfigFromStorage() || defaultMySQLConfig
    const client = createMySQLClient(config)

    try {
      const mysqlApp = client.convertToMySQLJobApplication(data)
      await client.addJobApplication(mysqlApp)
    } finally {
      await client.close()
    }
  }

  /**
   * 重试MySQL删除操作
   */
  private async retryMySQLDelete(data: { syncId: string }): Promise<void> {
    const config = loadMySQLConfigFromStorage() || defaultMySQLConfig
    const client = createMySQLClient(config)

    try {
      await client.deleteJobApplicationBySyncId(data.syncId)
    } finally {
      await client.close()
    }
  }

  /**
   * 获取双数据库状态
   */
  async getStatus(): Promise<{ local: boolean; mysql: boolean; failedOperations: number }> {
    const localStatus = true // 本地数据库总是可用的
    const mysqlStatus = await this.isMySQLAvailable()

    return {
      local: localStatus,
      mysql: mysqlStatus,
      failedOperations: this.failedOperations.length
    }
  }

  /**
   * 清除失败操作队列
   */
  clearFailedOperations(): void {
    this.failedOperations = []
    if (this.retryTimer) {
      clearTimeout(this.retryTimer)
      this.retryTimer = null
    }
  }
}

// 创建单例实例
export const dualDatabaseService = new DualDatabaseService()

// 求职申请相关类型定义

export interface JobApplication {
  id?: number
  syncId?: string // 用于双数据库同步的唯一标识符，不在UI中显示
  userId?: number
  username?: string
  company: string
  applicationLink: string
  priority: number
  industry: string
  tags: string
  position: string
  location: string
  progress: string
  status: string
  progressDate: string
  applicationDate: string
  notes: string
  referralCode: string
  createdAt?: Date
  updatedAt?: Date
}



export interface JobApplicationFilter {
  company?: string
  industry?: string
  position?: string
  location?: string
  progress?: string
  status?: string
  priority?: number
}

export interface JobApplicationSort {
  field: keyof JobApplication
  order: 'asc' | 'desc'
}

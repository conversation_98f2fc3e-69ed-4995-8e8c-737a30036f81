import { defineStore } from 'pinia'
import { ref, computed, watch } from 'vue'
import { databaseService } from '../services/database'
import { userService } from '../services/userService'
import { dualDatabaseService } from '../services/dualDatabaseService'

import type { JobApplication } from '../types/jobApplication'

// 本地存储键名
const STORAGE_KEY = 'job_applications_data'
const BACKUP_KEY = 'job_applications_backup'

// 数据验证函数
const validateJobApplication = (data: any): data is JobApplication => {
  return (
    typeof data === 'object' &&
    typeof data.id === 'number' &&
    typeof data.company === 'string' &&
    typeof data.applicationLink === 'string' &&
    typeof data.priority === 'number' &&
    typeof data.industry === 'string' &&
    typeof data.tags === 'string' &&
    typeof data.position === 'string' &&
    typeof data.location === 'string' &&
    typeof data.progress === 'string' &&
    typeof data.status === 'string' &&
    typeof data.progressDate === 'string' &&
    typeof data.applicationDate === 'string' &&
    typeof data.notes === 'string' &&
    typeof data.referralCode === 'string'
  )
}

export const useJobApplicationStore = defineStore('jobApplication', () => {
  // 状态
  const applications = ref<JobApplication[]>([])
  const loading = ref(false)
  const currentPage = ref(1)
  const pageSize = ref(10)

  // 计算属性
  const total = computed(() => applications.value.length)
  
  const paginatedApplications = computed(() => {
    const start = (currentPage.value - 1) * pageSize.value
    const end = start + pageSize.value
    return applications.value.slice(start, end)
  })

  // 从数据库加载数据
  const loadFromStorage = async () => {
    try {
      loading.value = true
      const currentUser = userService.getCurrentUser()

      if (currentUser) {
        // 加载当前用户的数据
        const userApplications = await databaseService.getJobApplicationsByUsername(currentUser.username)
        applications.value = userApplications
        console.log(`从数据库加载了 ${userApplications.length} 条记录`)
      } else {
        // 如果没有当前用户，加载所有数据
        const allApplications = await databaseService.getAllJobApplications()
        applications.value = allApplications
        console.log(`从数据库加载了 ${allApplications.length} 条记录`)
      }
    } catch (error) {
      console.error('加载数据失败:', error)
      applications.value = []
    } finally {
      loading.value = false
    }
  }

  // 从备份加载数据
  const loadFromBackup = () => {
    try {
      const backup = localStorage.getItem(BACKUP_KEY)
      if (backup) {
        const data = JSON.parse(backup)
        if (Array.isArray(data) && data.every(validateJobApplication)) {
          applications.value = data
          saveToStorage() // 恢复主数据
          console.log('从备份成功恢复数据')
          return
        }
      }
    } catch (error) {
      console.error('备份数据也损坏:', error)
    }
    // 如果备份也失败，初始化默认数据
    initializeDefaultData()
  }

  // 保存到本地存储（包含备份）
  const saveToStorage = () => {
    try {
      const jsonData = JSON.stringify(applications.value)

      // 创建备份
      const currentData = localStorage.getItem(STORAGE_KEY)
      if (currentData) {
        localStorage.setItem(BACKUP_KEY, currentData)
      }

      // 保存新数据
      localStorage.setItem(STORAGE_KEY, jsonData)

      console.log(`数据已保存，共 ${applications.value.length} 条记录`)
    } catch (error) {
      console.error('保存数据失败:', error)
    }
  }

  // 初始化默认数据（空数据）
  const initializeDefaultData = () => {
    applications.value = []
    saveToStorage()
    console.log('求职申请表已初始化为空数据')
  }


  // 添加新记录
  const addApplication = async (application: Omit<JobApplication, 'id' | 'createdAt' | 'updatedAt'>) => {
    try {
      // 获取当前日期作为默认投递时间
      const today = new Date().toISOString().split('T')[0]

      const newApplication = {
        ...application,
        // 如果没有提供投递时间，则使用当前日期
        applicationDate: application.applicationDate || today
      }

      // 使用双数据库服务添加记录
      const result = await dualDatabaseService.addJobApplication(newApplication)

      if (!result.success && !result.localSuccess) {
        throw new Error(result.error || '添加记录失败')
      }

      // 更新本地状态
      const createdApplication: JobApplication = {
        ...newApplication,
        id: result.data?.localId,
        syncId: result.data?.syncId,
        createdAt: new Date(),
        updatedAt: new Date()
      }

      applications.value.unshift(createdApplication)

      // 提供用户反馈
      if (result.success) {
        console.log(`添加新记录成功: ${newApplication.company} - ${newApplication.position}`)
      } else if (result.localSuccess && !result.mysqlSuccess) {
        console.warn(`记录已保存到本地，但MySQL同步失败: ${result.mysqlError}`)
      }

      return createdApplication
    } catch (error) {
      console.error('添加记录失败:', error)
      throw error
    }
  }

  // 更新记录
  const updateApplication = async (id: number, updates: Partial<JobApplication>) => {
    try {
      const index = applications.value.findIndex(app => app.id === id)

      if (index !== -1) {
        const originalApp = applications.value[index]
        const updatedApp = { ...originalApp, ...updates, updatedAt: new Date() }

        // 更新数据库
        await databaseService.updateJobApplication(id, updates)

        // 更新本地状态
        applications.value[index] = updatedApp



        console.log(`更新记录 ID:${id}`, Object.keys(updates))
        return applications.value[index]
      }

      console.warn(`未找到 ID 为 ${id} 的记录`)
      return null
    } catch (error) {
      console.error('更新记录失败:', error)
      throw error
    }
  }

  // 删除记录
  const deleteApplication = async (id: number) => {
    try {
      const index = applications.value.findIndex(app => app.id === id)
      if (index !== -1) {
        const deletedApp = applications.value[index]

        // 使用双数据库服务删除记录
        const result = await dualDatabaseService.deleteJobApplication(id, deletedApp.syncId)

        if (!result.success && !result.localSuccess) {
          throw new Error(result.error || '删除记录失败')
        }

        // 从内存中删除
        applications.value.splice(index, 1)
        saveToStorage()

        // 提供用户反馈
        if (result.success) {
          console.log(`删除记录成功: ${deletedApp.company} - ${deletedApp.position}`)
        } else if (result.localSuccess && !result.mysqlSuccess) {
          console.warn(`记录已从本地删除，但MySQL同步失败: ${result.mysqlError}`)
        }

        return true
      }
      console.warn(`未找到 ID 为 ${id} 的记录`)
      return false
    } catch (error) {
      console.error('删除记录失败:', error)
      throw error
    }
  }

  // 分页相关方法
  const setPage = (page: number) => {
    currentPage.value = page
  }

  const setPageSize = (size: number) => {
    pageSize.value = size
    currentPage.value = 1 // 重置到第一页
  }

  // 重置数据到初始状态
  const resetToDefault = () => {
    initializeDefaultData()
  }

  // 获取数据统计信息
  const getStatistics = () => {
    const stats = {
      total: applications.value.length,
      priority: { high: 0, medium: 0, low: 0 },
      progress: { signed: 0, interviewing: 0, applied: 0, rejected: 0 },
      status: { waiting: 0, passed: 0, failed: 0, abandoned: 0 }
    }

    applications.value.forEach(app => {
      // 优先级统计
      switch (app.priority) {
        case 1: stats.priority.high++; break
        case 2: stats.priority.medium++; break
        case 3: stats.priority.low++; break
      }

      // 进展统计
      if (app.progress === '签约') {
        stats.progress.signed++
      } else if (['一面', '二面', '三面', 'HR终面', '谈offer'].includes(app.progress)) {
        stats.progress.interviewing++
      } else if (app.progress === '已投递') {
        stats.progress.applied++
      }

      // 状态统计
      if (['等消息', '等我回复', '等待开始'].includes(app.status)) {
        stats.status.waiting++
      } else if (app.status === '已过') {
        stats.status.passed++
      } else if (['未过', '被调剂'].includes(app.status)) {
        stats.status.failed++
      } else if (['已放弃', '解约'].includes(app.status)) {
        stats.status.abandoned++
      }
    })

    return stats
  }

  // 获取双数据库状态
  const getDatabaseStatus = async () => {
    return await dualDatabaseService.getStatus()
  }

  // 监控数据变化，自动保存
  watch(
    applications,
    (newApplications) => {
      if (newApplications.length > 0) {
        // 延迟保存，避免频繁写入
        setTimeout(() => {
          saveToStorage()
        }, 100)
      }
    },
    { deep: true }
  )

  // 定期备份数据（每5分钟）
  if (typeof window !== 'undefined') {
    setInterval(() => {
      if (applications.value.length > 0) {
        const backupData = JSON.stringify(applications.value)
        localStorage.setItem(`${BACKUP_KEY}_${Date.now()}`, backupData)

        // 清理旧备份（保留最近3个）
        const keys = Object.keys(localStorage).filter(key => key.startsWith(`${BACKUP_KEY}_`))
        if (keys.length > 3) {
          keys.sort().slice(0, -3).forEach(key => {
            localStorage.removeItem(key)
          })
        }
      }
    }, 5 * 60 * 1000) // 5分钟
  }



  return {
    // 状态
    applications,
    loading,
    currentPage,
    pageSize,

    // 计算属性
    total,
    paginatedApplications,

    // 方法
    loadFromStorage,
    saveToStorage,
    addApplication,
    updateApplication,
    deleteApplication,
    setPage,
    setPageSize,
    resetToDefault,
    getStatistics,
    getDatabaseStatus
  }
})

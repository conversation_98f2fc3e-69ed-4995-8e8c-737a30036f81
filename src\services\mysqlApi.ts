// MySQL数据库API服务

export interface MySQLConfig {
  host: string
  port: number
  user: string
  password: string
  database: string
}

export interface MySQLRecord {
  id?: number
  updateTime?: string
  company: string
  applicationLink: string
  industry: string
  tags: string
  batch: string
  isHot: string
  position: string
  location: string
  deadline: string
}

// 用户数据接口
export interface MySQLUser {
  id?: number
  username: string
  created_at?: string
  updated_at?: string
}

// 求职申请数据接口（对应userdata表结构）
export interface MySQLJobApplication {
  id?: number
  syncId?: string
  username: string
  公司: string
  投递链接: string
  重视度: number
  职位: string
  地点: string
  进展: string
  状态: string
  进展时间: string
  投递时间: string
  备注: string
  内推码: string
}



/**
 * MySQL数据库API客户端
 */
export class MySQLApiClient {
  private config: MySQLConfig
  private connection: any = null
  private isConnecting: boolean = false
  private connectionRetries: number = 0
  private maxRetries: number = 3

  constructor(config: MySQLConfig) {
    this.config = config
  }

  /**
   * 创建数据库连接
   */
  private async createConnection(): Promise<any> {
    // 尝试使用Tauri API连接MySQL
    try {
      const { invoke } = await import('@tauri-apps/api/core')

      return {
        query: async (sql: string, params?: any[]) => {
          try {
            const result = await invoke('mysql_query', {
              config: this.config,
              sql,
              params: params || []
            })
            // Tauri返回的是数组格式，需要包装成mysql2的格式
            return [result]
          } catch (error) {
            console.error('MySQL查询失败:', error)
            // 重置连接以便下次重新连接
            this.connection = null
            throw error
          }
        },
        end: () => {
          // Tauri环境中连接由Rust管理
          this.connection = null
        }
      }
    } catch (error) {
      // 如果无法导入Tauri API，说明在纯浏览器环境中
      console.error('Tauri API不可用:', error)
      throw new Error('MySQL连接仅在Tauri桌面应用中支持，当前环境不支持数据库连接')
    }
  }

  /**
   * 获取数据库连接（带重试机制）
   */
  private async getConnection(): Promise<any> {
    if (this.connection) {
      return this.connection
    }

    if (this.isConnecting) {
      // 等待当前连接完成
      while (this.isConnecting && this.connectionRetries < this.maxRetries) {
        await new Promise(resolve => setTimeout(resolve, 100))
      }
      if (this.connection) {
        return this.connection
      }
    }

    this.isConnecting = true

    try {
      this.connection = await this.createConnection()
      this.connectionRetries = 0
      console.log('MySQL连接创建成功')
      return this.connection
    } catch (error) {
      this.connectionRetries++
      console.error(`MySQL连接失败 (尝试 ${this.connectionRetries}/${this.maxRetries}):`, error)

      if (this.connectionRetries >= this.maxRetries) {
        throw new Error(`MySQL连接失败，已重试 ${this.maxRetries} 次: ${error}`)
      }

      // 等待一段时间后重试
      await new Promise(resolve => setTimeout(resolve, 1000 * this.connectionRetries))
      return this.getConnection()
    } finally {
      this.isConnecting = false
    }
  }

  /**
   * 测试连接
   */
  async testConnection(): Promise<boolean> {
    try {
      const connection = await this.getConnection()
      await connection.query('SELECT 1')
      console.log('MySQL连接测试成功')
      return true
    } catch (error) {
      console.error('MySQL连接测试失败:', error)
      this.connection = null // 重置连接
      return false
    }
  }

  /**
   * 重置连接
   */
  async resetConnection(): Promise<void> {
    if (this.connection) {
      try {
        await this.connection.end()
      } catch (error) {
        console.warn('关闭旧连接时出错:', error)
      }
    }
    this.connection = null
    this.connectionRetries = 0
    this.isConnecting = false
  }

  /**
   * 获取热门校招汇总表数据
   */
  async getHotAutumnData(): Promise<MySQLRecord[]> {
    try {
      const connection = await this.getConnection()
      const [rows] = await connection.query('SELECT * FROM 热门校招汇总')
      const records = this.convertMySQLRecords(rows)
      console.log(`获取热门校招汇总数据成功: ${records.length} 条记录`)
      return records
    } catch (error) {
      console.error('获取热门校招汇总表数据失败:', error)
      await this.resetConnection() // 重置连接
      throw new Error(`获取热门校招汇总表数据失败: ${error}`)
    }
  }

  /**
   * 获取热门实习汇总表数据
   */
  async getHotInternshipData(): Promise<MySQLRecord[]> {
    try {
      const connection = await this.getConnection()
      const [rows] = await connection.query('SELECT * FROM 热门实习汇总')
      const records = this.convertMySQLRecords(rows)
      console.log(`获取热门实习汇总数据成功: ${records.length} 条记录`)
      return records
    } catch (error) {
      console.error('获取热门实习汇总表数据失败:', error)
      await this.resetConnection()
      throw new Error(`获取热门实习汇总表数据失败: ${error}`)
    }
  }

  /**
   * 获取国企央企汇总表数据
   */
  async getStateOwnedData(): Promise<MySQLRecord[]> {
    try {
      const connection = await this.getConnection()
      const [rows] = await connection.query('SELECT * FROM 国企央企汇总')
      const records = this.convertMySQLRecords(rows)
      console.log(`获取国企央企汇总数据成功: ${records.length} 条记录`)
      return records
    } catch (error) {
      console.error('获取国企央企汇总表数据失败:', error)
      await this.resetConnection()
      throw new Error(`获取国企央企汇总表数据失败: ${error}`)
    }
  }





  /**
   * 获取所有用户（从userdata表中提取）
   */
  async getAllUsers(): Promise<MySQLUser[]> {
    try {
      const connection = await this.getConnection()
      const [rows] = await connection.query(
        `SELECT DISTINCT username FROM userdata WHERE username IS NOT NULL AND username != '' ORDER BY username`
      )
      return rows.map((row: any, index: number) => ({
        id: index + 1,
        username: row.username,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }))
    } catch (error) {
      console.error('获取用户列表失败:', error)
      await this.resetConnection()
      throw new Error(`获取用户列表失败: ${error}`)
    }
  }

  /**
   * 添加求职申请到userdata表
   */
  async addJobApplication(application: Omit<MySQLJobApplication, 'id'>): Promise<number> {
    try {
      const connection = await this.getConnection()

      const sql = `INSERT INTO userdata (syncId, username, \`公司\`, \`投递链接\`, \`重视度\`, \`职位\`, \`地点\`, \`进展\`, \`状态\`, \`进展时间\`, \`投递时间\`, \`备注\`, \`内推码\`) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`

      const values = [
        application.syncId || '',
        application.username,
        application.公司,
        application.投递链接,
        application.重视度,
        application.职位,
        application.地点,
        application.进展,
        application.状态,
        application.进展时间,
        application.投递时间,
        application.备注,
        application.内推码
      ]

      console.log('执行MySQL添加求职申请SQL:', sql)
      console.log('参数值:', values)

      const [result] = await connection.query(sql, values)
      const insertId = (result as any).insertId

      console.log('MySQL求职申请添加成功，ID:', insertId)
      return insertId
    } catch (error) {
      console.error('MySQL添加求职申请失败:', error)
      await this.resetConnection()
      throw new Error(`MySQL添加求职申请失败: ${error}`)
    }
  }

  /**
   * 根据syncId删除求职申请
   */
  async deleteJobApplicationBySyncId(syncId: string): Promise<boolean> {
    try {
      const connection = await this.getConnection()

      const sql = `DELETE FROM userdata WHERE syncId = ?`
      const values = [syncId]

      console.log('执行MySQL删除求职申请SQL:', sql)
      console.log('参数值:', values)

      const [result] = await connection.query(sql, values)
      const affectedRows = (result as any).affectedRows

      console.log('MySQL求职申请删除成功，影响行数:', affectedRows)
      return affectedRows > 0
    } catch (error) {
      console.error('MySQL删除求职申请失败:', error)
      await this.resetConnection()
      throw new Error(`MySQL删除求职申请失败: ${error}`)
    }
  }

  /**
   * 根据syncId获取求职申请
   */
  async getJobApplicationBySyncId(syncId: string): Promise<MySQLJobApplication | null> {
    try {
      const connection = await this.getConnection()

      const sql = `SELECT * FROM userdata WHERE syncId = ?`
      const values = [syncId]

      const [rows] = await connection.query(sql, values)
      const rowsArray = rows as any[]

      if (rowsArray.length > 0) {
        const row = rowsArray[0]
        return {
          id: row.id,
          syncId: row.syncId,
          username: row.username,
          公司: row.公司 || '',
          投递链接: row.投递链接 || '',
          重视度: row.重视度 || 2,
          职位: row.职位 || '',
          地点: row.地点 || '',
          进展: row.进展 || '',
          状态: row.状态 || '',
          进展时间: row.进展时间 || '',
          投递时间: row.投递时间 || '',
          备注: row.备注 || '',
          内推码: row.内推码 || ''
        }
      }

      return null
    } catch (error) {
      console.error('MySQL根据syncId获取求职申请失败:', error)
      await this.resetConnection()
      throw new Error(`MySQL根据syncId获取求职申请失败: ${error}`)
    }
  }

  /**
   * 获取用户的所有求职申请
   */
  async getJobApplicationsByUsername(username: string): Promise<MySQLJobApplication[]> {
    try {
      const connection = await this.getConnection()

      const sql = `SELECT * FROM userdata WHERE username = ? AND \`公司\` != '' ORDER BY id DESC`
      const values = [username]

      const [rows] = await connection.query(sql, values)
      const rowsArray = rows as any[]

      return rowsArray.map(row => ({
        id: row.id,
        syncId: row.syncId,
        username: row.username,
        公司: row.公司 || '',
        投递链接: row.投递链接 || '',
        重视度: row.重视度 || 2,
        职位: row.职位 || '',
        地点: row.地点 || '',
        进展: row.进展 || '',
        状态: row.状态 || '',
        进展时间: row.进展时间 || '',
        投递时间: row.投递时间 || '',
        备注: row.备注 || '',
        内推码: row.内推码 || ''
      }))
    } catch (error) {
      console.error('MySQL获取用户求职申请失败:', error)
      await this.resetConnection()
      throw new Error(`MySQL获取用户求职申请失败: ${error}`)
    }
  }











  /**
   * 转换MySQL记录为标准格式
   */
  private convertMySQLRecords(rows: any[]): MySQLRecord[] {
    return rows.map((row, index) => ({
      id: row.id || Date.now() + index,
      updateTime: row.更新 || row.updateTime || '',
      company: row.公司 || row.company || '',
      applicationLink: row.投递链接 || row.applicationLink || '',
      industry: row.行业 || row.industry || '',
      tags: row.标签 || row.tags || '',
      batch: row.批次 || row.batch || '',
      isHot: String(row.热门 || row.isHot || ''),
      position: row.职位 || row.position || '',
      location: row.地点 || row.location || '',
      deadline: row.投递截至 || row.deadline || ''
    }))
  }

  /**
   * 将本地JobApplication转换为MySQL格式
   */
  convertToMySQLJobApplication(localApp: any): Omit<MySQLJobApplication, 'id'> {
    return {
      syncId: localApp.syncId || '',
      username: localApp.username || '',
      公司: localApp.company || '',
      投递链接: localApp.applicationLink || '',
      重视度: localApp.priority || 2,
      职位: localApp.position || '',
      地点: localApp.location || '',
      进展: localApp.progress || '',
      状态: localApp.status || '',
      进展时间: localApp.progressDate || '',
      投递时间: localApp.applicationDate || '',
      备注: localApp.notes || '',
      内推码: localApp.referralCode || ''
    }
  }

  /**
   * 将MySQL格式转换为本地JobApplication
   */
  convertFromMySQLJobApplication(mysqlApp: MySQLJobApplication): any {
    return {
      id: mysqlApp.id,
      syncId: mysqlApp.syncId,
      username: mysqlApp.username,
      company: mysqlApp.公司,
      applicationLink: mysqlApp.投递链接,
      priority: mysqlApp.重视度,
      industry: '', // MySQL userdata表中没有这个字段
      tags: '', // MySQL userdata表中没有这个字段
      position: mysqlApp.职位,
      location: mysqlApp.地点,
      progress: mysqlApp.进展,
      status: mysqlApp.状态,
      progressDate: mysqlApp.进展时间,
      applicationDate: mysqlApp.投递时间,
      notes: mysqlApp.备注,
      referralCode: mysqlApp.内推码
    }
  }

  /**
   * 转换MySQL用户记录
   */
  private convertMySQLUsers(rows: any[]): MySQLUser[] {
    return rows.map(row => ({
      id: row.id,
      username: row.username,
      created_at: row.created_at,
      updated_at: row.updated_at
    }))
  }



  /**
   * 关闭连接
   */
  async close(): Promise<void> {
    try {
      if (this.connection) {
        await this.connection.end()
        console.log('MySQL连接已关闭')
      }
    } catch (error) {
      console.warn('关闭MySQL连接时出错:', error)
    } finally {
      this.connection = null
      this.connectionRetries = 0
      this.isConnecting = false
    }
  }
}

/**
 * 创建MySQL API客户端实例
 */
export function createMySQLClient(config: MySQLConfig): MySQLApiClient {
  return new MySQLApiClient(config)
}

/**
 * 验证MySQL配置是否完整
 */
export function validateMySQLConfig(config: Partial<MySQLConfig>): config is MySQLConfig {
  return !!(config.host && config.port && config.user && config.password && config.database)
}

/**
 * 默认MySQL配置
 */
export const defaultMySQLConfig: MySQLConfig = {
  host: 'mysql5.sqlpub.com',
  port: 3310,
  user: 'fish2609',
  password: 'Hrk6OCOXYo39ToeS',
  database: 'jobfish_pool'
}

/**
 * 从localStorage加载MySQL配置
 */
export function loadMySQLConfigFromStorage(): MySQLConfig | null {
  try {
    const saved = localStorage.getItem('mysql-config')
    if (saved) {
      const config = JSON.parse(saved)
      if (validateMySQLConfig(config)) {
        return config
      }
    }
  } catch (error) {
    console.error('加载MySQL配置失败:', error)
  }
  return null
}

/**
 * 保存MySQL配置到localStorage
 */
export function saveMySQLConfigToStorage(config: MySQLConfig): void {
  try {
    localStorage.setItem('mysql-config', JSON.stringify(config))
  } catch (error) {
    console.error('保存MySQL配置失败:', error)
  }
}

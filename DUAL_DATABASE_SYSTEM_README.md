# 双数据库同步系统使用说明

## 概述

本系统实现了求职申请数据的双数据库同步功能，能够同时将数据保存到本地数据库（IndexedDB）和云端MySQL数据库，确保数据的安全性和可用性。

## 核心功能

### 1. 双数据库同步
- **本地数据库**: 使用IndexedDB存储，确保离线可用
- **云端数据库**: 使用MySQL存储，确保数据备份和多设备同步
- **同步策略**: 同时写入两个数据库，本地优先

### 2. 唯一ID系统
- 每条记录都有一个唯一的`syncId`字段
- `syncId`用于在本地和云端数据库之间保持数据一致性
- `syncId`不在用户界面中显示，仅用于内部同步

### 3. 错误处理和恢复
- **网络故障处理**: 当MySQL不可用时，数据仍保存到本地
- **重试机制**: 失败的操作会自动重试
- **状态指示**: 实时显示数据库连接状态

## 使用方法

### 基本操作

#### 添加记录
```javascript
// 通过UI添加记录时，系统会自动：
// 1. 生成唯一的syncId
// 2. 保存到本地数据库
// 3. 同步到MySQL数据库
// 4. 提供操作结果反馈
```

#### 删除记录
```javascript
// 通过UI删除记录时，系统会自动：
// 1. 从本地数据库删除
// 2. 从MySQL数据库删除
// 3. 提供操作结果反馈
```

### 状态监控

#### 数据库连接状态
- **绿色指示器**: 数据库连接正常
- **红色指示器**: 数据库连接异常
- **本地数据库**: 始终显示绿色（本地数据库总是可用）
- **MySQL数据库**: 根据实际连接状态显示

#### 操作反馈
- **成功**: 两个数据库都操作成功
- **部分成功**: 本地成功，MySQL失败（会自动重试）
- **失败**: 本地操作失败

## 配置选项

### 双数据库服务配置
```javascript
// 可以通过以下方式配置双数据库服务
dualDatabaseService.setConfig({
  enableMySQL: true,        // 是否启用MySQL同步
  rollbackOnFailure: true, // MySQL失败时是否回滚本地操作
  retryAttempts: 3,         // 重试次数
  retryDelay: 1000         // 重试延迟（毫秒）
})
```

### MySQL连接配置
- 使用现有的MySQL配置系统
- 配置路径：管理员面板 → MySQL数据库设置
- 默认使用`userdata`表存储求职申请数据

## 数据结构

### 本地数据库（IndexedDB）
```javascript
{
  id: number,           // 本地ID
  syncId: string,       // 同步ID（唯一标识符）
  username: string,     // 用户名
  company: string,      // 公司名称
  position: string,     // 职位
  // ... 其他字段
}
```

### MySQL数据库（userdata表）
```sql
CREATE TABLE userdata (
  id INT AUTO_INCREMENT PRIMARY KEY,
  syncId VARCHAR(255),
  username VARCHAR(255),
  `公司` VARCHAR(255),
  `职位` VARCHAR(255),
  -- ... 其他字段（使用中文列名）
)
```

## 错误处理

### 常见错误场景

1. **MySQL连接失败**
   - 现象：MySQL状态指示器显示红色
   - 处理：数据仍保存到本地，稍后自动重试同步

2. **网络中断**
   - 现象：操作提示"MySQL同步失败"
   - 处理：失败的操作会加入重试队列，网络恢复后自动重试

3. **数据冲突**
   - 现象：同一syncId在两个数据库中不一致
   - 处理：以本地数据为准，重新同步到MySQL

### 手动恢复

如果遇到数据不一致问题，可以：

1. **清除失败操作队列**
```javascript
dualDatabaseService.clearFailedOperations()
```

2. **手动检查数据库状态**
```javascript
const status = await dualDatabaseService.getStatus()
console.log(status)
```

## 开发和测试

### 测试工具
系统提供了完整的测试工具，在开发环境中可以通过浏览器控制台使用：

```javascript
// 运行所有测试
await window.dualDatabaseTest.runAllTests()

// 单独测试添加功能
await window.dualDatabaseTest.testAddRecord()

// 单独测试删除功能
await window.dualDatabaseTest.testDeleteRecord()

// 测试数据库状态
await window.dualDatabaseTest.testDatabaseStatus()

// 测试syncId唯一性
await window.dualDatabaseTest.testSyncIdUniqueness()
```

### 调试信息
系统会在控制台输出详细的调试信息，包括：
- 数据库操作日志
- 错误信息和重试记录
- 同步状态变化

## 注意事项

1. **数据一致性**: syncId是保证数据一致性的关键，请勿手动修改
2. **网络依赖**: MySQL同步需要网络连接，离线时仅本地数据可用
3. **性能考虑**: 大量数据操作时，MySQL同步可能会有延迟
4. **备份策略**: 建议定期备份MySQL数据库

## 故障排除

### 常见问题

**Q: MySQL状态一直显示红色**
A: 检查MySQL配置是否正确，网络连接是否正常

**Q: 数据只保存到本地，不同步到MySQL**
A: 检查MySQL配置，确认`enableMySQL`设置为true

**Q: 删除记录后MySQL中仍有数据**
A: 可能是网络问题导致删除失败，系统会自动重试

**Q: 如何确认数据已同步到MySQL**
A: 查看操作反馈信息，或直接查询MySQL数据库

## 更新日志

- **v1.0.0**: 初始版本，实现基本的双数据库同步功能
- 支持添加和删除操作的双数据库同步
- 实现唯一ID系统和错误处理机制
- 提供实时状态监控和用户反馈

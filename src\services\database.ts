import Dexie, { Table } from 'dexie'
import type { RecruitmentSummary } from '../types/recruitment'
import type { JobApplication } from '../types/jobApplication'

// 用户设置接口
export interface UserSettings {
  id?: number
  key: string
  value: string
  updatedAt: Date
}

// 飞书配置接口
export interface FeishuConfig {
  id?: number
  appToken: string
  tableId: string
  internshipTableId: string
  stateOwnedTableId: string
  appId: string
  appSecret: string
  updatedAt: Date
}

// 字段映射接口
export interface FieldMapping {
  id?: number
  localField: string
  feishuField: string
  updatedAt: Date
}

// 数据库类
export class RecruitmentDatabase extends Dexie {
  // 表定义
  userSettings!: Table<UserSettings>
  feishuConfig!: Table<FeishuConfig>
  fieldMappings!: Table<FieldMapping>
  jobApplications!: Table<JobApplication>
  hotAutumnRecruitments!: Table<RecruitmentSummary>
  hotInternshipRecruitments!: Table<RecruitmentSummary>
  stateOwnedRecruitments!: Table<RecruitmentSummary>

  constructor() {
    super('RecruitmentDatabase')
    
    this.version(1).stores({
      userSettings: '++id, key, value, updatedAt',
      feishuConfig: '++id, appToken, tableId, internshipTableId, stateOwnedTableId, appId, appSecret, updatedAt',
      fieldMappings: '++id, localField, feishuField, updatedAt',
      jobApplications: '++id, syncId, userId, username, company, position, status, applicationDate, deadline, notes, priority, tags, contact, salary, location, createdAt, updatedAt',
      hotAutumnRecruitments: '++id, updateTime, company, applicationLink, industry, tags, batch, isHot, position, location, deadline',
      hotInternshipRecruitments: '++id, updateTime, company, applicationLink, industry, tags, batch, isHot, position, location, deadline',
      stateOwnedRecruitments: '++id, updateTime, company, applicationLink, industry, tags, batch, isHot, position, location, deadline'
    })
  }
}

// 创建数据库实例
export const db = new RecruitmentDatabase()

// 生成唯一同步ID的工具函数
export const generateSyncId = (): string => {
  // 使用时间戳 + 随机数生成唯一ID
  const timestamp = Date.now().toString(36)
  const randomPart = Math.random().toString(36).substring(2, 15)
  return `sync_${timestamp}_${randomPart}`
}

// 数据库连接状态监控
let isReconnecting = false

// 监控数据库连接状态
const monitorDatabaseConnection = () => {
  // 定期检查数据库连接状态
  setInterval(async () => {
    try {
      if (!db.isOpen() && !isReconnecting) {
        console.warn('检测到数据库连接断开，尝试重新连接...')
        isReconnecting = true
        await db.open()
        console.log('数据库重新连接成功')
        isReconnecting = false
      }
    } catch (error) {
      console.error('数据库重连检查失败:', error)
      isReconnecting = false
    }
  }, 30000) // 每30秒检查一次
}

// 启动连接监控
if (typeof window !== 'undefined') {
  monitorDatabaseConnection()
}

// 数据备份到 localStorage 的工具函数
export const backupToLocalStorage = async (tableName: string, data: any[]) => {
  try {
    const backupKey = `backup_${tableName}`
    localStorage.setItem(backupKey, JSON.stringify(data))
    console.log(`数据已备份到 localStorage: ${backupKey}`)
  } catch (error) {
    console.error('备份到 localStorage 失败:', error)
  }
}

// 从 localStorage 恢复数据的工具函数
export const restoreFromLocalStorage = (tableName: string): any[] => {
  try {
    const backupKey = `backup_${tableName}`
    const data = localStorage.getItem(backupKey)
    if (data) {
      console.log(`从 localStorage 恢复数据: ${backupKey}`)
      return JSON.parse(data)
    }
  } catch (error) {
    console.error('从 localStorage 恢复数据失败:', error)
  }
  return []
}

// 检查数据是否可以被序列化
function checkSerializable(data: any, path: string = 'root'): string[] {
  const issues: string[] = []

  if (data === null || data === undefined) {
    return issues
  }

  if (typeof data === 'function') {
    issues.push(`${path}: 包含函数`)
    return issues
  }

  if (typeof data === 'symbol') {
    issues.push(`${path}: 包含Symbol`)
    return issues
  }

  if (data instanceof Date) {
    return issues // Date对象可以序列化
  }

  if (Array.isArray(data)) {
    data.forEach((item, index) => {
      issues.push(...checkSerializable(item, `${path}[${index}]`))
    })
    return issues
  }

  if (typeof data === 'object') {
    // 检查循环引用
    try {
      JSON.stringify(data)
    } catch (error) {
      if (error instanceof TypeError && error.message.includes('circular')) {
        issues.push(`${path}: 包含循环引用`)
        return issues
      }
    }

    for (const key in data) {
      if (data.hasOwnProperty(key)) {
        const value = data[key]
        if (value === undefined) {
          issues.push(`${path}.${key}: 值为undefined`)
        } else {
          issues.push(...checkSerializable(value, `${path}.${key}`))
        }
      }
    }
  }

  return issues
}

// 数据清理函数，确保数据可以被IndexedDB序列化
function sanitizeData(data: any): any {
  if (data === null || data === undefined) {
    return data
  }

  if (Array.isArray(data)) {
    return data.map(item => sanitizeData(item))
  }

  if (typeof data === 'object') {
    const sanitized: any = {}
    for (const key in data) {
      if (data.hasOwnProperty(key)) {
        const value = data[key]
        // 跳过函数、undefined、symbol等不可序列化的值
        if (typeof value === 'function' || typeof value === 'symbol' || value === undefined) {
          console.warn(`跳过不可序列化的属性: ${key} (类型: ${typeof value})`)
          continue
        }
        // 递归清理嵌套对象
        sanitized[key] = sanitizeData(value)
      }
    }
    return sanitized
  }

  // 基本类型直接返回
  return data
}

// 数据库服务类
export class DatabaseService {

  // 数据库连接状态检查和重连
  private async ensureDatabaseOpen(): Promise<void> {
    try {
      // 检查数据库是否已打开
      if (!db.isOpen()) {
        console.log('数据库未打开，尝试重新连接...')
        await db.open()
        console.log('数据库重新连接成功')
      }
    } catch (error) {
      console.error('数据库连接失败:', error)
      throw new Error('数据库连接失败，请重启应用')
    }
  }

  // 带重试机制的数据库操作包装器
  private async withDatabaseRetry<T>(operation: () => Promise<T>, operationName: string): Promise<T> {
    const maxRetries = 3
    let lastError: any

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        await this.ensureDatabaseOpen()
        return await operation()
      } catch (error: any) {
        lastError = error
        console.error(`${operationName} 失败 (尝试 ${attempt}/${maxRetries}):`, error)

        // 如果是数据库关闭错误，尝试重新打开
        if (error.name === 'DatabaseClosedError' || error.message?.includes('DatabaseClosedError')) {
          console.log('检测到数据库关闭错误，尝试重新连接...')
          try {
            db.close()
            await db.open()
            console.log('数据库重新连接成功')
          } catch (reconnectError) {
            console.error('数据库重连失败:', reconnectError)
          }
        }

        // 如果不是最后一次尝试，等待一段时间后重试
        if (attempt < maxRetries) {
          await new Promise(resolve => setTimeout(resolve, 1000 * attempt))
        }
      }
    }

    throw lastError
  }

  // ==================== 用户设置 ====================
  
  async getSetting(key: string): Promise<string | null> {
    try {
      const setting = await db.userSettings.where('key').equals(key).first()
      return setting ? setting.value : null
    } catch (error) {
      console.error('获取设置失败:', error)
      return null
    }
  }

  async setSetting(key: string, value: string): Promise<void> {
    try {
      const existing = await db.userSettings.where('key').equals(key).first()
      if (existing) {
        await db.userSettings.update(existing.id!, { value, updatedAt: new Date() })
      } else {
        await db.userSettings.add({ key, value, updatedAt: new Date() })
      }
      console.log(`设置已保存: ${key} = ${value}`)
    } catch (error) {
      console.error('保存设置失败:', error)
      throw error
    }
  }

  // ==================== 飞书配置 ====================
  
  async getFeishuConfig(): Promise<FeishuConfig | null> {
    try {
      return await db.feishuConfig.orderBy('id').last() || null
    } catch (error) {
      console.error('获取飞书配置失败:', error)
      return null
    }
  }

  async saveFeishuConfig(config: Omit<FeishuConfig, 'id' | 'updatedAt'>): Promise<void> {
    try {
      // 清空旧配置
      await db.feishuConfig.clear()
      // 保存新配置
      await db.feishuConfig.add({ ...config, updatedAt: new Date() })
      console.log('飞书配置已保存')
    } catch (error) {
      console.error('保存飞书配置失败:', error)
      throw error
    }
  }

  // ==================== 字段映射 ====================
  
  async getFieldMappings(): Promise<Record<string, string>> {
    try {
      const mappings = await db.fieldMappings.toArray()
      const result: Record<string, string> = {}
      mappings.forEach(mapping => {
        result[mapping.localField] = mapping.feishuField
      })
      return result
    } catch (error) {
      console.error('获取字段映射失败:', error)
      return {}
    }
  }

  async saveFieldMappings(mappings: Record<string, string>): Promise<void> {
    try {
      // 清空旧映射
      await db.fieldMappings.clear()
      // 保存新映射
      const mappingArray = Object.entries(mappings).map(([localField, feishuField]) => ({
        localField,
        feishuField,
        updatedAt: new Date()
      }))
      await db.fieldMappings.bulkAdd(mappingArray)
      console.log('字段映射已保存')
    } catch (error) {
      console.error('保存字段映射失败:', error)
      throw error
    }
  }

  // ==================== 求职申请 ====================

  async getAllJobApplications(): Promise<JobApplication[]> {
    try {
      return await db.jobApplications.orderBy('createdAt').reverse().toArray()
    } catch (error) {
      console.error('获取求职申请失败:', error)
      return []
    }
  }

  async getJobApplicationsByUser(userId: number): Promise<JobApplication[]> {
    try {
      const applications = await db.jobApplications
        .where('userId')
        .equals(userId)
        .toArray()

      // 手动排序，因为Dexie的where().equals()后不能直接链式调用orderBy()
      return applications.sort((a, b) => {
        const dateA = a.createdAt ? new Date(a.createdAt).getTime() : 0
        const dateB = b.createdAt ? new Date(b.createdAt).getTime() : 0
        return dateB - dateA // 降序排列
      })
    } catch (error) {
      console.error('获取用户求职申请失败:', error)
      return []
    }
  }

  async getJobApplicationsByUsername(username: string): Promise<JobApplication[]> {
    try {
      const applications = await db.jobApplications
        .where('username')
        .equals(username)
        .toArray()

      // 手动排序，因为Dexie的where().equals()后不能直接链式调用orderBy()
      return applications.sort((a, b) => {
        const dateA = a.createdAt ? new Date(a.createdAt).getTime() : 0
        const dateB = b.createdAt ? new Date(b.createdAt).getTime() : 0
        return dateB - dateA // 降序排列
      })
    } catch (error) {
      console.error('获取用户求职申请失败:', error)
      return []
    }
  }

  async addJobApplication(application: Omit<JobApplication, 'id' | 'createdAt' | 'updatedAt'>): Promise<number> {
    try {
      const now = new Date()

      // 确保有用户信息
      if (!application.userId && !application.username) {
        throw new Error('必须提供用户ID或用户名')
      }

      // 如果没有提供syncId，生成一个新的
      const applicationWithSyncId = {
        ...application,
        syncId: application.syncId || generateSyncId(),
        createdAt: now,
        updatedAt: now
      }

      const result = await db.jobApplications.add(applicationWithSyncId)

      // 备份所有求职申请数据
      await this.backupJobApplications()

      return result
    } catch (error) {
      console.error('添加求职申请失败:', error)
      throw error
    }
  }

  async batchAddJobApplications(applications: Omit<JobApplication, 'id' | 'createdAt' | 'updatedAt'>[]): Promise<number[]> {
    try {
      const now = new Date()
      const applicationsWithTimestamp = applications.map(app => ({
        ...app,
        syncId: app.syncId || generateSyncId(),
        createdAt: now,
        updatedAt: now
      }))

      const results = await db.jobApplications.bulkAdd(applicationsWithTimestamp, { allKeys: true })

      // 备份所有求职申请数据
      await this.backupJobApplications()

      return results as number[]
    } catch (error) {
      console.error('批量添加求职申请失败:', error)
      throw error
    }
  }

  async replaceUserJobApplications(userId: number, username: string, applications: Omit<JobApplication, 'id' | 'createdAt' | 'updatedAt' | 'userId' | 'username'>[]): Promise<void> {
    try {
      // 开始事务
      await db.transaction('rw', db.jobApplications, async () => {
        // 删除该用户的所有现有数据
        await db.jobApplications.where('userId').equals(userId).delete()

        // 添加新数据
        if (applications.length > 0) {
          const now = new Date()
          const applicationsWithUser = applications.map(app => ({
            ...app,
            userId,
            username,
            createdAt: now,
            updatedAt: now
          }))

          await db.jobApplications.bulkAdd(applicationsWithUser)
        }
      })

      // 备份数据
      await this.backupJobApplications()

      console.log(`替换用户 ${username} 的求职申请数据，共 ${applications.length} 条`)
    } catch (error) {
      console.error('替换用户求职申请数据失败:', error)
      throw error
    }
  }

  // 备份求职申请数据
  private async backupJobApplications() {
    try {
      const allApplications = await db.jobApplications.toArray()
      await backupToLocalStorage('jobApplications', allApplications)
    } catch (error) {
      console.error('备份求职申请数据失败:', error)
    }
  }

  async updateJobApplication(id: number, updates: Partial<JobApplication>): Promise<void> {
    try {
      await db.jobApplications.update(id, { ...updates, updatedAt: new Date() })
      console.log(`求职申请已更新: ID ${id}`)
    } catch (error) {
      console.error('更新求职申请失败:', error)
      throw error
    }
  }

  async deleteJobApplication(id: number): Promise<void> {
    try {
      await db.jobApplications.delete(id)
      console.log(`求职申请已删除: ID ${id}`)
    } catch (error) {
      console.error('删除求职申请失败:', error)
      throw error
    }
  }

  async getJobApplicationBySyncId(syncId: string): Promise<JobApplication | null> {
    try {
      const application = await db.jobApplications.where('syncId').equals(syncId).first()
      return application || null
    } catch (error) {
      console.error('根据syncId获取求职申请失败:', error)
      return null
    }
  }

  async deleteJobApplicationBySyncId(syncId: string): Promise<boolean> {
    try {
      const application = await this.getJobApplicationBySyncId(syncId)
      if (application && application.id) {
        await db.jobApplications.delete(application.id)
        console.log(`求职申请已删除: syncId ${syncId}`)
        return true
      }
      return false
    } catch (error) {
      console.error('根据syncId删除求职申请失败:', error)
      throw error
    }
  }

  // ==================== 热门秋招汇总 ====================
  
  async getAllHotAutumnRecruitments(): Promise<RecruitmentSummary[]> {
    return this.withDatabaseRetry(async () => {
      const data = await db.hotAutumnRecruitments.toArray()
      if (data.length === 0) {
        // 如果数据库为空，尝试从备份恢复
        console.log('数据库为空，尝试从 localStorage 恢复数据')
        const backupData = restoreFromLocalStorage('hotAutumnRecruitments')
        if (backupData.length > 0) {
          await this.saveHotAutumnRecruitments(backupData)
          return backupData
        }
      }
      return data
    }, '获取热门秋招数据').catch((error) => {
      console.error('获取热门秋招数据失败:', error)
      // 如果数据库出错，尝试从备份恢复
      const backupData = restoreFromLocalStorage('hotAutumnRecruitments')
      if (backupData.length > 0) {
        console.log('从 localStorage 备份恢复数据')
        return backupData
      }
      return []
    })
  }

  async saveHotAutumnRecruitments(recruitments: RecruitmentSummary[]): Promise<void> {
    return this.withDatabaseRetry(async () => {
      console.log('开始保存热门秋招数据，原始数据条数:', recruitments.length)

      // 检查数据序列化问题
      if (recruitments.length > 0) {
        const issues = checkSerializable(recruitments[0], 'recruitments[0]')
        if (issues.length > 0) {
          console.warn('数据序列化问题:', issues)
        }
      }

      // 清理数据，确保可以被IndexedDB序列化
      const sanitizedRecruitments = sanitizeData(recruitments)
      console.log('清理后数据条数:', sanitizedRecruitments.length)

      // 备份到 localStorage
      await backupToLocalStorage('hotAutumnRecruitments', sanitizedRecruitments)

      if (sanitizedRecruitments.length > 0) {
        console.log('清理后数据示例:', sanitizedRecruitments[0])
      }

      // 使用事务确保数据一致性
      await db.transaction('rw', db.hotAutumnRecruitments, async () => {
        await db.hotAutumnRecruitments.clear()
        await db.hotAutumnRecruitments.bulkAdd(sanitizedRecruitments)
      })

      console.log(`热门秋招数据已保存: ${sanitizedRecruitments.length} 条记录`)
    }, '保存热门秋招数据').catch(async (error) => {
      console.error('保存热门秋招数据失败:', error)
      const err = error as Error
      console.error('错误类型:', err.name)
      console.error('错误消息:', err.message)

      // 如果是数据克隆错误，尝试更激进的清理
      if (err.name === 'DataCloneError') {
        console.log('尝试使用JSON序列化/反序列化清理数据')
        try {
          const jsonCleanedData = JSON.parse(JSON.stringify(recruitments))
          await this.withDatabaseRetry(async () => {
            await db.transaction('rw', db.hotAutumnRecruitments, async () => {
              await db.hotAutumnRecruitments.clear()
              await db.hotAutumnRecruitments.bulkAdd(jsonCleanedData)
            })
          }, 'JSON清理后保存数据')
          console.log(`使用JSON清理后保存成功: ${jsonCleanedData.length} 条记录`)
          return
        } catch (jsonError) {
          console.error('JSON清理也失败:', jsonError)
        }
      }

      throw error
    })
  }

  async updateHotAutumnRecruitment(id: number, updates: Partial<RecruitmentSummary>): Promise<void> {
    try {
      await db.hotAutumnRecruitments.update(id, updates)
      console.log(`热门秋招记录已更新: ID ${id}`)
    } catch (error) {
      console.error('更新热门秋招记录失败:', error)
      throw error
    }
  }

  // ==================== 热门实习汇总 ====================
  
  async getAllHotInternshipRecruitments(): Promise<RecruitmentSummary[]> {
    try {
      return await db.hotInternshipRecruitments.toArray()
    } catch (error) {
      console.error('获取热门实习数据失败:', error)
      return []
    }
  }

  async saveHotInternshipRecruitments(recruitments: RecruitmentSummary[]): Promise<void> {
    try {
      const sanitizedRecruitments = sanitizeData(recruitments)

      // 备份到 localStorage
      await backupToLocalStorage('hotInternshipRecruitments', sanitizedRecruitments)

      await db.hotInternshipRecruitments.clear()
      await db.hotInternshipRecruitments.bulkAdd(sanitizedRecruitments)
      console.log(`热门实习数据已保存: ${sanitizedRecruitments.length} 条记录`)
    } catch (error) {
      console.error('保存热门实习数据失败:', error)
      throw error
    }
  }

  async updateHotInternshipRecruitment(id: number, updates: Partial<RecruitmentSummary>): Promise<void> {
    try {
      await db.hotInternshipRecruitments.update(id, updates)
      console.log(`热门实习记录已更新: ID ${id}`)
    } catch (error) {
      console.error('更新热门实习记录失败:', error)
      throw error
    }
  }

  // ==================== 国企央企汇总 ====================
  
  async getAllStateOwnedRecruitments(): Promise<RecruitmentSummary[]> {
    try {
      return await db.stateOwnedRecruitments.toArray()
    } catch (error) {
      console.error('获取国企央企数据失败:', error)
      return []
    }
  }

  async saveStateOwnedRecruitments(recruitments: RecruitmentSummary[]): Promise<void> {
    try {
      const sanitizedRecruitments = sanitizeData(recruitments)

      // 备份到 localStorage
      await backupToLocalStorage('stateOwnedRecruitments', sanitizedRecruitments)

      await db.stateOwnedRecruitments.clear()
      await db.stateOwnedRecruitments.bulkAdd(sanitizedRecruitments)
      console.log(`国企央企数据已保存: ${sanitizedRecruitments.length} 条记录`)
    } catch (error) {
      console.error('保存国企央企数据失败:', error)
      throw error
    }
  }

  async updateStateOwnedRecruitment(id: number, updates: Partial<RecruitmentSummary>): Promise<void> {
    try {
      await db.stateOwnedRecruitments.update(id, updates)
      console.log(`国企央企记录已更新: ID ${id}`)
    } catch (error) {
      console.error('更新国企央企记录失败:', error)
      throw error
    }
  }

  // ==================== 数据管理 ====================
  
  async clearAllData(): Promise<void> {
    try {
      await Promise.all([
        db.userSettings.clear(),
        db.feishuConfig.clear(),
        db.fieldMappings.clear(),
        db.jobApplications.clear(),
        db.hotAutumnRecruitments.clear(),
        db.hotInternshipRecruitments.clear(),
        db.stateOwnedRecruitments.clear()
      ])
      console.log('所有数据已清空')
    } catch (error) {
      console.error('清空数据失败:', error)
      throw error
    }
  }

  async exportAllData(): Promise<any> {
    try {
      const [
        userSettings,
        feishuConfig,
        fieldMappings,
        jobApplications,
        hotAutumnRecruitments,
        hotInternshipRecruitments,
        stateOwnedRecruitments
      ] = await Promise.all([
        db.userSettings.toArray(),
        db.feishuConfig.toArray(),
        db.fieldMappings.toArray(),
        db.jobApplications.toArray(),
        db.hotAutumnRecruitments.toArray(),
        db.hotInternshipRecruitments.toArray(),
        db.stateOwnedRecruitments.toArray()
      ])

      return {
        userSettings,
        feishuConfig,
        fieldMappings,
        jobApplications,
        hotAutumnRecruitments,
        hotInternshipRecruitments,
        stateOwnedRecruitments,
        exportTime: new Date().toISOString()
      }
    } catch (error) {
      console.error('导出数据失败:', error)
      throw error
    }
  }

  async getDataStats(): Promise<any> {
    try {
      const [
        userSettingsCount,
        feishuConfigCount,
        fieldMappingsCount,
        jobApplicationsCount,
        hotAutumnCount,
        hotInternshipCount,
        stateOwnedCount
      ] = await Promise.all([
        db.userSettings.count(),
        db.feishuConfig.count(),
        db.fieldMappings.count(),
        db.jobApplications.count(),
        db.hotAutumnRecruitments.count(),
        db.hotInternshipRecruitments.count(),
        db.stateOwnedRecruitments.count()
      ])

      return {
        userSettings: userSettingsCount,
        feishuConfig: feishuConfigCount,
        fieldMappings: fieldMappingsCount,
        jobApplications: jobApplicationsCount,
        hotAutumnRecruitments: hotAutumnCount,
        hotInternshipRecruitments: hotInternshipCount,
        stateOwnedRecruitments: stateOwnedCount
      }
    } catch (error) {
      console.error('获取数据统计失败:', error)
      return {}
    }
  }
}

// 创建数据库服务实例
export const databaseService = new DatabaseService()

import { dualDatabaseService } from '../services/dualDatabaseService'
import { databaseService } from '../services/database'
import type { JobApplication } from '../types/jobApplication'

/**
 * 双数据库系统测试工具
 */
export class DualDatabaseTest {
  
  /**
   * 测试添加记录功能
   */
  async testAddRecord(): Promise<{ success: boolean; message: string }> {
    try {
      console.log('开始测试添加记录功能...')
      
      const testApplication: Omit<JobApplication, 'id' | 'createdAt' | 'updatedAt'> = {
        username: '测试用户',
        company: '测试公司',
        applicationLink: 'https://test.com/apply',
        priority: 1,
        industry: '互联网',
        tags: 'Vue,TypeScript',
        position: '前端工程师',
        location: '北京',
        progress: '已投递',
        status: '等消息',
        progressDate: '2024-01-15',
        applicationDate: '2024-01-15',
        notes: '这是一条测试记录',
        referralCode: 'TEST001'
      }

      const result = await dualDatabaseService.addJobApplication(testApplication)
      
      if (result.success || result.localSuccess) {
        console.log('添加记录测试成功:', result)
        return {
          success: true,
          message: `添加成功 - 本地: ${result.localSuccess ? '✓' : '✗'}, MySQL: ${result.mysqlSuccess ? '✓' : '✗'}`
        }
      } else {
        console.error('添加记录测试失败:', result)
        return {
          success: false,
          message: `添加失败: ${result.error || '未知错误'}`
        }
      }
    } catch (error) {
      console.error('添加记录测试异常:', error)
      return {
        success: false,
        message: `测试异常: ${error instanceof Error ? error.message : '未知错误'}`
      }
    }
  }

  /**
   * 测试删除记录功能
   */
  async testDeleteRecord(): Promise<{ success: boolean; message: string }> {
    try {
      console.log('开始测试删除记录功能...')
      
      // 首先获取一条记录用于测试删除
      const allApplications = await databaseService.getAllJobApplications()
      const testRecord = allApplications.find(app => app.company === '测试公司')
      
      if (!testRecord || !testRecord.id) {
        return {
          success: false,
          message: '未找到测试记录，请先运行添加测试'
        }
      }

      const result = await dualDatabaseService.deleteJobApplication(testRecord.id, testRecord.syncId)
      
      if (result.success || result.localSuccess) {
        console.log('删除记录测试成功:', result)
        return {
          success: true,
          message: `删除成功 - 本地: ${result.localSuccess ? '✓' : '✗'}, MySQL: ${result.mysqlSuccess ? '✓' : '✗'}`
        }
      } else {
        console.error('删除记录测试失败:', result)
        return {
          success: false,
          message: `删除失败: ${result.error || '未知错误'}`
        }
      }
    } catch (error) {
      console.error('删除记录测试异常:', error)
      return {
        success: false,
        message: `测试异常: ${error instanceof Error ? error.message : '未知错误'}`
      }
    }
  }

  /**
   * 测试数据库状态检查
   */
  async testDatabaseStatus(): Promise<{ success: boolean; message: string }> {
    try {
      console.log('开始测试数据库状态检查...')
      
      const status = await dualDatabaseService.getStatus()
      
      console.log('数据库状态:', status)
      
      return {
        success: true,
        message: `状态检查成功 - 本地: ${status.local ? '✓' : '✗'}, MySQL: ${status.mysql ? '✓' : '✗'}, 失败操作: ${status.failedOperations}`
      }
    } catch (error) {
      console.error('状态检查测试异常:', error)
      return {
        success: false,
        message: `测试异常: ${error instanceof Error ? error.message : '未知错误'}`
      }
    }
  }

  /**
   * 测试syncId唯一性
   */
  async testSyncIdUniqueness(): Promise<{ success: boolean; message: string }> {
    try {
      console.log('开始测试syncId唯一性...')
      
      const allApplications = await databaseService.getAllJobApplications()
      const syncIds = allApplications
        .filter(app => app.syncId)
        .map(app => app.syncId!)
      
      const uniqueSyncIds = new Set(syncIds)
      
      if (syncIds.length === uniqueSyncIds.size) {
        return {
          success: true,
          message: `syncId唯一性测试通过 - 共 ${syncIds.length} 个记录，全部唯一`
        }
      } else {
        return {
          success: false,
          message: `syncId唯一性测试失败 - 共 ${syncIds.length} 个记录，但只有 ${uniqueSyncIds.size} 个唯一值`
        }
      }
    } catch (error) {
      console.error('syncId唯一性测试异常:', error)
      return {
        success: false,
        message: `测试异常: ${error instanceof Error ? error.message : '未知错误'}`
      }
    }
  }

  /**
   * 运行所有测试
   */
  async runAllTests(): Promise<{ success: boolean; results: string[] }> {
    console.log('开始运行双数据库系统完整测试...')
    
    const results: string[] = []
    let allSuccess = true

    // 测试数据库状态
    const statusTest = await this.testDatabaseStatus()
    results.push(`状态检查: ${statusTest.message}`)
    if (!statusTest.success) allSuccess = false

    // 测试syncId唯一性
    const uniquenessTest = await this.testSyncIdUniqueness()
    results.push(`唯一性检查: ${uniquenessTest.message}`)
    if (!uniquenessTest.success) allSuccess = false

    // 测试添加记录
    const addTest = await this.testAddRecord()
    results.push(`添加测试: ${addTest.message}`)
    if (!addTest.success) allSuccess = false

    // 测试删除记录
    const deleteTest = await this.testDeleteRecord()
    results.push(`删除测试: ${deleteTest.message}`)
    if (!deleteTest.success) allSuccess = false

    console.log('测试完成，结果:', results)
    
    return {
      success: allSuccess,
      results
    }
  }
}

// 导出测试实例
export const dualDatabaseTest = new DualDatabaseTest()

// 在开发环境中将测试工具挂载到全局对象
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  (window as any).dualDatabaseTest = dualDatabaseTest
}

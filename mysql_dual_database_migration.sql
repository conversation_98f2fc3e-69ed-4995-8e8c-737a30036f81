-- =====================================================
-- 双数据库系统 MySQL 迁移脚本
-- 为现有的 userdata 表添加 syncId 字段支持
-- =====================================================

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- =====================================================
-- 1. 检查并添加 syncId 字段到 userdata 表
-- =====================================================

-- 添加 syncId 字段（如果不存在）
ALTER TABLE userdata 
ADD COLUMN IF NOT EXISTS syncId VARCHAR(255) DEFAULT NULL 
COMMENT '双数据库同步唯一标识符' 
AFTER id;

-- 为 syncId 字段添加索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_syncId ON userdata(syncId);

-- =====================================================
-- 2. 为现有记录生成 syncId（如果需要）
-- =====================================================

-- 为没有 syncId 的现有记录生成唯一标识符
UPDATE userdata 
SET syncId = CONCAT('sync_', UNIX_TIMESTAMP(NOW()), '_', id, '_', SUBSTRING(MD5(RAND()), 1, 8))
WHERE syncId IS NULL OR syncId = '';

-- =====================================================
-- 3. 验证数据完整性
-- =====================================================

-- 检查是否有重复的 syncId
SELECT syncId, COUNT(*) as count 
FROM userdata 
WHERE syncId IS NOT NULL AND syncId != ''
GROUP BY syncId 
HAVING COUNT(*) > 1;

-- 如果上面的查询返回结果，说明有重复的 syncId，需要手动处理

-- =====================================================
-- 4. 创建用于双数据库系统的视图（可选）
-- =====================================================

-- 创建一个视图来方便查询求职申请数据
CREATE OR REPLACE VIEW job_applications_view AS
SELECT 
    id,
    syncId,
    username,
    `公司` as company,
    `投递链接` as applicationLink,
    `重视度` as priority,
    `职位` as position,
    `地点` as location,
    `进展` as progress,
    `状态` as status,
    `进展时间` as progressDate,
    `投递时间` as applicationDate,
    `备注` as notes,
    `内推码` as referralCode
FROM userdata
WHERE `公司` IS NOT NULL AND `公司` != '';

-- =====================================================
-- 5. 创建用于监控同步状态的表（可选）
-- =====================================================

-- 创建同步日志表来跟踪双数据库操作
CREATE TABLE IF NOT EXISTS sync_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    syncId VARCHAR(255) NOT NULL,
    operation_type ENUM('add', 'update', 'delete') NOT NULL,
    status ENUM('success', 'failed', 'retry') NOT NULL,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_syncId_log (syncId),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
COMMENT='双数据库同步操作日志';

-- =====================================================
-- 6. 存储过程：清理旧的同步日志（可选）
-- =====================================================

DELIMITER //

CREATE PROCEDURE IF NOT EXISTS CleanOldSyncLogs()
BEGIN
    -- 删除30天前的同步日志
    DELETE FROM sync_log 
    WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY);
    
    SELECT ROW_COUNT() as deleted_rows;
END //

DELIMITER ;

-- =====================================================
-- 7. 验证迁移结果
-- =====================================================

-- 显示 userdata 表的结构
DESCRIBE userdata;

-- 显示 syncId 字段的统计信息
SELECT 
    COUNT(*) as total_records,
    COUNT(syncId) as records_with_syncId,
    COUNT(DISTINCT syncId) as unique_syncIds
FROM userdata;

-- 显示最近的几条记录
SELECT id, syncId, username, `公司`, `职位` 
FROM userdata 
WHERE `公司` IS NOT NULL AND `公司` != ''
ORDER BY id DESC 
LIMIT 5;

SET FOREIGN_KEY_CHECKS = 1;

-- =====================================================
-- 使用说明：
-- 1. 在执行此脚本前，请备份您的数据库
-- 2. 此脚本是幂等的，可以安全地多次执行
-- 3. 执行后请检查验证查询的结果
-- 4. 如果发现 syncId 重复，请手动处理冲突记录
-- =====================================================

-- 执行完成后的检查清单：
-- □ userdata 表已添加 syncId 字段
-- □ 所有现有记录都有唯一的 syncId
-- □ 没有重复的 syncId
-- □ 索引已正确创建
-- □ 可选的视图和日志表已创建
